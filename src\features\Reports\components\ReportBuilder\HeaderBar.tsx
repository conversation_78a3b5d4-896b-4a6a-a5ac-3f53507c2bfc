import {
  Ri<PERSON>rt<PERSON><PERSON>ill,
  Ri<PERSON>rticle<PERSON>ine,
  RiCheckboxCircleLine,
  RiEdit2Fill,
  RiGroup3Fill,
  RiSave2Fill,
  RiCodeLine,
  RiHtml5Line,
  RiBracesLine,
} from "@remixicon/react";
import { useAddReport } from "features/Reports/api";
import { setReportTitle } from "features/Reports/store";
import useReportStore, {
  resetReportState,
} from "features/Reports/store/report";
import { useInvalidateQuery } from "hooks";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";
import ActionButton from "../ActionButton";
import ReportPreview from "./ReportPreview";
// TEMPORARY: Imports for JSON conversion (will be removed later)
import { generateJSON } from "@tiptap/html";
import StarterKit from "@tiptap/starter-kit";
import Highlight from "@tiptap/extension-highlight";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";

// TEMPORARY: Extensions for JSON conversion (will be removed later)
const extensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  GraphPlaceholder,
  ContentLock,
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
];

interface HeaderBarProps {
  title?: string;
}

const HeaderBar = ({ title = "" }: HeaderBarProps) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  // TEMPORARY: Toggle for JSON format (will be removed later)
  const [useJsonFormat, setUseJsonFormat] = useState(false);
  const titleRef = useRef<HTMLInputElement>(null);
  const reportinfo = useReportStore((state) => state.reportInfo);
  const [invalidateQueries] = useInvalidateQuery();

  const { mutateAsync: addReport } = useAddReport();

  useEffect(() => {
    setLocalTitle(title);
    if (titleRef.current) {
      titleRef.current.value = title || "";
    }
  }, [title]);

  const handleEditTitle = () => {
    setIsEditingTitle(true);
    setTimeout(() => titleRef.current?.focus(), 0); // Focus input after re-render
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        setReportTitle(newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };

  const handleCheckCompliance = () => {
    console.log("Check compliance clicked");
  };

  const handleShowPreview = () => {
    setShowPreview(true);
  };

  // TEMPORARY: Toggle handler for JSON format (will be removed later)
  const handleToggleFormat = () => {
    setUseJsonFormat(!useJsonFormat);
    toast.success(`Switched to ${!useJsonFormat ? "JSON" : "HTML"} format`);
  };

  const saveReport = async () => {
    try {
      // TEMPORARY: Transform content based on format toggle (will be removed later)
      let payload = reportinfo;

      if (useJsonFormat) {
        // Convert HTML content to JSON format
        payload = {
          ...reportinfo,
          sections: reportinfo.sections.map((section) => {
            if (
              typeof section.content === "object" &&
              section.content !== null &&
              section.content.type === "html"
            ) {
              try {
                const jsonContent = generateJSON(
                  section.content.content,
                  extensions
                );
                return {
                  ...section,
                  content: jsonContent,
                };
              } catch (error) {
                console.error("Error converting HTML to JSON:", error);
                return section; // Keep original if conversion fails
              }
            }
            return section;
          }),
        };
      }

      const response: any = await addReport(payload);
      if (response?.success) {
        resetReportState();
        toast.success(response?.message);
        invalidateQueries(["reports-list"]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleSaveReport = () => {
    if (!reportinfo.title) {
      toast.error("Please enter report title");
      return;
    }
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: saveReport,
        content: {
          heading: "Save Report",
          description: `Are you sure you want to save this report?`,
        },
        iconColor: "#ad986f",
        icon: RiArticleLine,
      },
    });
  };

  return (
    <>
      <div className="header d-flex gap-3">
        <div
          className={`header-title rounded w-100 position-relative ${isEditingTitle ? "border-1 border-brown bg-white" : ""}`}
        >
          <input
            type="text"
            className="report-title-input w-100 text-center fw-bold"
            placeholder="Enter report title..."
            readOnly={!isEditingTitle}
            ref={titleRef}
            defaultValue={localTitle}
            onKeyDown={handleTitleKeyDown}
            onBlur={handleTitleBlur}
          />
          {isEditingTitle ? (
            <button className="edit-title-btn">
              <RiCheckboxCircleLine color="#ad986f" />
            </button>
          ) : (
            <button className="edit-title-btn" onClick={handleEditTitle}>
              <RiEdit2Fill />
            </button>
          )}
        </div>
        <div className="header-actions d-flex gap-2">
          <ActionButton icon={RiArticleFill} onClick={handleCheckCompliance} />
          <ActionButton icon={RiGroup3Fill} onClick={handleShowPreview} />
          {/* TEMPORARY: JSON format toggle button (will be removed later) */}
          <ActionButton
            icon={useJsonFormat ? RiBracesLine : RiHtml5Line}
            onClick={handleToggleFormat}
            title={`Currently using ${useJsonFormat ? "JSON" : "HTML"} format - Click to switch`}
            className={useJsonFormat ? "active" : ""}
          />
          <ActionButton icon={RiSave2Fill} onClick={handleSaveReport} />
        </div>
      </div>

      {showPreview && (
        <ReportPreview
          show={showPreview}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

export default HeaderBar;
